# 前后端联动方案（MVP）：在 IndexedDB 存储 ChatSummary Buffer 与历史聊天记录，让前端传递 chatSummaryBuffer 的相关信息给后端

> 目标读者：产品/前端/后端工程师（MVP 阶段）。
> 目标：**不引入后端数据库**，在浏览器 IndexedDB 中存储每个会话的聊天内容与 Chat Summary Buffer；在聊天过程中**正确携带上下文调用 FastAPI RAG 后端**；并**通过 visit_session 管理自动清理老旧记录**。本文仅为“实施说明录”，不包含代码。

---

## 0. 范围与原则

1. 仅覆盖前端与后端之间的**数据契约**、**数据落盘策略**与**生命周期**。
2. **存储位置**：浏览器 IndexedDB（每个域名约 50MB-1GB+ 配额，支持结构化数据和索引）。
3. **后端状态**：完全无状态。后端不保存会话和 chatSummaryBuffer，仅根据请求内的上下文完成 RAG 推理并返回更新后的 chatSummaryBuffer。
4. **MVP 从简**：先跑通“单机、单用户、单域名”体验；未来再演进到 Cookie 登录 + 数据库持久化。

---

## 1. 名词与角色

- **会话（Session）**：一次持续的对话线程，具有唯一标识（session_id，使用 nanoid 生成）。
- **chatSummaryBuffer**：LlamaIndex 后端生成的聊天摘要缓冲区，包含压缩的历史对话和相关元数据。
- **frontend_session_chat_history**：前端维护的完整聊天历史记录，用于 UI 展示。
- **visit_session**：用户在当前域的浏览器访问会话，从打开第一个标签页开始到关闭最后一个标签页结束。
- **mode**：聊天模式，包括 "query"（检索模式）和 "condense_question"（问题压缩模式）。

---

## 2. 存储布局（IndexedDB 数据库设计）

### 2.1 数据库结构设计（前端实现）

**数据库名称**：`ChatSessionDB`

**对象存储（Object Store）**：

- **sessions**：存储所有会话数据
  - 主键：session_id（string，使用 nanoid 生成）
  - 索引：
    - `updated_at`：按更新时间排序
    - `created_at`：按创建时间排序
    - `mode`：按聊天模式过滤

### 2.2 会话数据结构（前端实现）

每个会话对象包含以下必需字段：

```
{
  session_id: string,                    // 必需：使用 nanoid 生成的唯一标识
  frontend_session_chat_history: array, // 必需：前端维护的完整聊天记录
  chatSummaryBuffer: object|null,       // 必需：LlamaIndex 后端生成的摘要缓冲区
  mode: string,                         // 必需："query" 或 "condense_question"
  course_id: string|null,               // 可选：课程ID
  course_material_id: string|null,      // 可选：课程材料ID
  title: string,                        // 会话标题（基于首轮用户输入生成）
  created_at: number,                   // 创建时间戳
  updated_at: number,                   // 最后更新时间戳
  last_accessed_at: number              // 最后访问时间戳
}
```

设计意图：

- 每个会话一个完整对象，避免数据碎片化
- 利用 IndexedDB 原生索引功能，无需单独维护会话索引
- chatSummaryBuffer 与 frontend_session_chat_history 的部分数据重复是可接受的

---

## 3. 前后端数据契约与交互流程

### 3.1 完整交互流程说明

#### 流程概述

1. **前端发送请求**：携带问题、chatSummaryBuffer、session_id、mode 等信息给后端
2. **后端处理请求**：根据 mode 和 chatSummaryBuffer 生成聊天引擎，产生回答，返回答案和更新后的 chatSummaryBuffer
3. **前端处理响应**：显示答案给用户，更新 frontend_session_chat_history，如果 chatSummaryBuffer 有更新则同步到 IndexedDB
4. **visit_session 管理**：在用户打开第一个标签页时执行垃圾清理，关闭最后一个标签页时结束 visit_session

### 3.2 前端请求载荷（前端实现）

```
{
  session_id: string,                    // 必需：会话唯一标识
  question: string,                      // 必需：用户当前输入的问题
  mode: string,                          // 必需："query" 或 "condense_question"
  chatSummaryBuffer: object|null,        // 必需：如果有就携带，没有则为 null
  course_id: string|null,               // 可选：课程ID
  course_material_id: string|null       // 可选：课程材料ID
}
```

### 3.3 后端响应载荷（后端实现）

```
{
  answer: string,                        // 必需：模型生成的回答内容
  chatSummaryBuffer: object|null,        // 必需：更新后的聊天摘要缓冲区
  sources: array,                        // 可选：来源信息（检索模式时提供）
  processing_time: number,               // 可选：处理时间
  metadata: object                       // 可选：其他元数据
}
```

---

## 4. 前端生命周期与策略（前端实现）

### 4.1 visit_session 管理

**触发时机与行为**：

- **开始 visit_session**：用户打开项目的第一个浏览器标签页时
  - 检查并删除所有超过 30 天未更新的会话数据（garbage cleanup）
  - 标记 visit_session 开始
- **visit_session 期间**：完全不考虑垃圾清理
- **结束 visit_session**：用户关闭项目的最后一个浏览器标签页时
  - 清理 visit_session 标记

**实现方式**：

- 使用 `beforeunload` 事件监听标签页关闭
- 使用 BroadcastChannel 或 localStorage 事件协调多标签页状态
- 使用计数器跟踪当前打开的标签页数量

### 4.2 新建会话

**触发条件**：用户点击「新对话」或访问无 session_id 的聊天路由

**行为**：

- 使用 nanoid 生成新的 session_id
- 初始化会话对象（空的 frontend_session_chat_history、null 的 chatSummaryBuffer）
- 将 session_id 写入 URL
- 保存到 IndexedDB

### 4.3 载入既有会话

**触发条件**：用户从会话列表点击进入或 URL 携带既有 session_id

**行为**：

- 从 IndexedDB 读取会话数据
- 渲染 frontend_session_chat_history
- 更新 last_accessed_at 时间戳

### 4.4 发送一轮消息

**行为**：

1. 组装请求载荷（包含当前会话的 chatSummaryBuffer）
2. 调用后端 API
3. 收到响应后：
   - 将用户问题和助手回答添加到 frontend_session_chat_history
   - 如果 chatSummaryBuffer 有更新，替换本地存储的 chatSummaryBuffer
   - 更新 updated_at 时间戳
   - 保存到 IndexedDB

### 4.5 会话列表管理

**获取会话列表**：

- 使用 IndexedDB 的 `updated_at` 索引按时间倒序查询
- 支持按 mode、course_id 等字段过滤

**会话搜索**：

- 利用 IndexedDB 索引进行高效查询
- 支持按标题、时间范围搜索
- 会话摘要（Chat Summary Buffer / Summary）：对早期历史消息的滚动压缩文本（“冷区”）。
- 近期消息（Recent Messages）：尚未被压缩进摘要的若干条原始消息（“热区”）。
- 会话索引（Session Index）：用于渲染会话列表的轻量清单（id、标题（暂时用 course_material_id 作为标题），会话创建时间、最后更新时间）。

---

## 2. 存储布局（LocalStorage 键空间规划）

为便于管理与清理，采用统一命名空间：

- 会话内容条目（每个 session 一条）：前缀 `chat:session:` 加上 session_id。
  内容包括：摘要、近期消息、元信息（标题（有 course_material_id 时，用 course_material_id 作为标题）、创建时间、更新时间、使用模型）。
- 会话索引清单：键名 `chat:sessions`。维护所有会话的轻量列表；用于侧边栏列表与快速打开。
- 可选：最近打开的会话指针：键名 `chat:current`。仅保存当前正在查看的 session_id。
- 可选：清理/任务协调用键（如 gc ？锁、最近一次清理时间戳等）。

设计意图：

- 每个会话一条“粗粒度对象”，避免碎片化键带来的维护复杂度。
- 索引与内容分离：索引便于快速渲染列表；内容包含完整上下文（同属于一个 session_id 的历史聊天记录，这个 session_id 的 chatSummaryBuffer 的内容）。
- 所有键统一前缀，便于后续批量清理。

---

## 3. 前后端数据契约（无状态 RAG 调用）

### 3.1 前端请求载荷应包含的要素

- 会话标识：session_id（来自 URL 或 UI 新建时生成）。
- 上下文内容：
  - 摘要（summary）：字符串。为空表示尚无摘要。
  - 近期消息（messages）：按时间从旧到新？排列；仅保留最近 N 条，N 由前端策略决定（见 §4）。
- 用户当轮输入：user_input（文本）。

### 3.2 后端响应应包含的要素

- 模型回复：assistant_output（文本）。
- ？RAG 证据摘要（如检索到的文档片段概览）。
- ？用量信息/调试信息（便于前端日志）。

设计意图：

- 后端不保管历史；所有需要的上下文由前端随每次请求携带。
- 后端需要把 chatSummaryBuffer 的哪些信息传给前端？

---

## 4. 前端生命周期与策略

### 4.1 新建会话

- 触发条件：用户点击「新对话」；或访问无 session_id 的聊天路由。
- 行为：
  - 生成新的 session_id；
  - 初始化一个会话对象（空摘要、空消息、默认元信息）；
  - 将 session_id 写入 URL，便于分享/刷新恢复；
  - 更新会话索引？？

### 4.2 载入既有会话

- 触发条件：用户从会话列表点击进入；或 URL 携带既有 session_id。
- 行为：
  - 从 LocalStorage 读取该会话条目；
  - 渲染摘要与近期消息；（好的，为了方便测试，当前还是渲染摘要吧）
  - 更新最近打开时间。

### 4.3 发送一轮消息（用户输入 → 调用后端 → 展示回复）

- 组装载荷：携带当前会话的摘要与“最近 N 条原始消息”以及本轮用户输入。
- 调用后端：POST 至 RAG 接口。
- 更新存储：将用户输入、模型给出的回答、chatSummaryBuffer 中的摘要更新到 localStorage

### 4.4 摘要滚动压缩（Summary 维护）（重新思考）

- 背景：为控制 token 与存储体积，需将较早的“近期消息”折叠入摘要。
- 触发策略（二选一或组合）：
  - 达到条数阈值（例如近期消息超过 M 条）；
  - 达到字符/近似 token 阈值（例如超过某上限）；
  - 页面空闲或本轮请求返回后执行。
- 行为：
  - 取出需要被折叠的早期消息，与现有摘要合并为“新摘要”；
  - 将这批早期消息从“近期消息”移出，仅保留最新的若干条；
  - 写回会话对象并更新时间。

说明：在 MVP 阶段，摘要生成可以直接调用现有后端的“摘要端点”（将旧摘要 + 待折叠消息发给后端，请其返回新摘要），从而保持“前端存储、后端算子”的职责划分。

### 4.5 30 天自动清理（GC）

- 清理对象：最后更新时间超过 30 天的会话条目。
- 触发时机（建议多点触发，确保稳定）：
  - 应用启动时；
  - 页面空闲时；
  - 固定间隔（例如每 24 小时检查一次）；
  - 可选：收到后端“需要降占用”的提示时。
- 行为：
  - 扫描命名空间内的所有会话键；
  - 比较每条会话的更新时间与当前时间；
  - 对超过 30 天的会话：从 LocalStorage 删除；同时从会话索引移除；
  - 记录最近一次清理时间用于节流。

### 4.6 多标签页协作（可选）

- 目标：避免同一会话在多个标签页同时执行“摘要合并”或“清理”产生冲突。
- 策略：
  - 使用浏览器的跨标签通信（如 BroadcastChannel 或 storage 事件）广播状态变化；
  - 使用轻量锁（例如设置一个带过期的锁键）防止并发操作；
  - 收到他页的变更通知后，仅更新 UI，不重复计算。

---

## 5. 数据结构约定（以文字描述，不给代码）

### 5.1 会话索引（chat:sessions）

- 存储一个列表，列表中每个元素至少包含：
  - id（session_id，与内容条目键后缀一致）；
  - title（人类可读的标题，可取首轮用户输入的片段或手动改名）；
  - updated_at（毫秒时间戳）；
  - 可选：created_at、model、system_prompt_hash、tag 等。

### 5.2 会话内容（chat:session:<session_id>）

- 包含下列字段：
  - summary：字符串。旧历史的滚动摘要；为空表示尚未形成摘要。
  - messages：列表。按时间从旧到新，仅保存最近若干条原始消息；每条消息含角色、文本与时间戳。
  - meta：对象。至少含 created_at、updated_at；可选 title、model、数据域标识等。
- 设计目的：
  - 读写原子化：每次写入时整体覆盖会话对象，避免局部键不一致；
  - 清理友好：过期删除时，仅需删除一个键并同步索引即可。

---

## 6. 容量与性能治理

- 近期消息限制：设置合理的上限（按条数或近似 token），超过即触发摘要折叠。
- 摘要长度限制：如摘要超过一定阈值，允许再次摘要（摘要的摘要），保持可控体积。
- 会话数量限制：例如最多保留最近 N 个活跃会话，超出后优先删除最旧且 30 天未访问的会话。
- 端到端开销：LocalStorage 读/写为同步操作，建议将频繁写入合并为“每轮对话后一次写入”，避免 UI 卡顿。

---

## 11. 与后端接口的协作要点（说明性，不给代码）

- RAG 主接口：接收会话摘要与最近消息，返回模型文本回复与可选证据摘要。后端不记录会话。
- 摘要维护接口（可复用 RAG 模型或单独的轻量模型）：接收“旧摘要 + 待折叠消息”，返回“新摘要”。
- 错误码与提示：明确区分参数问题、上下文超限、模型错误、网络问题，并在前端给出可操作化的提示（例如减少近期消息、稍后重试）。

---

## 12. 典型用户旅程（序列化流程）

1. 进入聊天页（URL 携带或生成 session_id）。
2. 从 LocalStorage 读取并渲染该会话（若不存在则初始化）。
3. 用户输入问题；前端组装“摘要 + 最近消息 + 本轮输入”并调用后端。
4. 收到回复后，前端将“用户输入 + 模型回复”追加到最近消息，并刷新更新时间。
5. 若超过阈值，触发摘要维护：将较早消息折叠进摘要，刷新会话对象。
6. 定期/启动时执行 30 天清理，删除过期会话并同步索引。

---

---

## 7. 当前代码问题分析与改进要点

### 7.1 现状问题识别

**前端问题：**

1. **聊天记录仅存内存**：当前使用 `window.chatMemory` 存储，页面刷新后丢失
2. **缺乏会话管理**：没有 session_id 概念，无法区分不同对话线程
3. **URL 状态缺失**：无法通过 URL 恢复特定会话
4. **模式切换上下文丢失**：检索模式与直接聊天模式切换时可能丢失上下文连续性

**后端问题：**

1. **直接聊天模式记忆处理不完整**：`_query_direct_chat` 方法中聊天记忆利用逻辑待完善
2. **摘要触发条件单一**：仅基于消息数量（20 条），未考虑 token 数量和内容长度
3. **两种模式记忆隔离**：检索模式和直接聊天模式的记忆管理策略需要统一

### 7.2 关键改进要点

**前端改进：**

1. **实现 session_id 机制**：

   - 页面加载时从 URL 获取或生成新的 session_id
   - 将 session_id 写入 URL，支持分享和刷新恢复
   - 基于 session_id 管理 localStorage 中的会话数据

2. **完善 localStorage 存储策略**：

   - 实现会话索引（`chat:sessions`）和会话内容（`chat:session:<id>`）分离
   - 每轮对话后立即持久化到 localStorage
   - 页面加载时从 localStorage 恢复会话状态

3. **增强 UI 交互**：

   - 添加会话列表侧边栏，支持快速切换历史会话
   - 显示会话标题（基于首轮用户输入生成）
   - 支持会话重命名和删除操作

4. **模式切换优化**：
   - 确保模式切换时保持同一会话的上下文连续性
   - 在请求中正确携带当前会话的摘要和近期消息

**后端改进：**

1. **完善直接聊天模式**：

   - 在 `_query_direct_chat` 中正确利用传入的 chat_memory
   - 构建包含历史上下文的对话，而非每次都是新对话

2. **优化摘要触发策略**：

   - 增加基于 token 数量的触发条件
   - 增加基于内容长度的触发条件
   - 支持手动触发摘要压缩

3. **统一记忆管理**：
   - 确保两种聊天模式使用相同的记忆更新逻辑
   - 优化摘要生成的提示词，提高摘要质量

---

## 8. 实现优先级与阶段规划

### 8.1 第一阶段（核心功能）

**前端任务**：

1. 实现 IndexedDB 数据库初始化和会话管理
2. 实现 session_id 生成（nanoid）和 URL 管理
3. 实现基础的前后端数据交互
4. 实现 visit_session 管理和垃圾清理

**后端任务**：

1. 修改 API 接口支持新的数据契约
2. 实现 chatSummaryBuffer 的接收和返回逻辑
3. 确保两种 mode 都正确处理 chatSummaryBuffer

### 8.2 第二阶段（用户体验）

**前端任务**：

1. 实现会话列表 UI 和搜索功能
2. 实现会话标题生成和编辑功能
3. 添加数据导出和导入功能

**后端任务**：

1. 优化 chatSummaryBuffer 的生成质量
2. 添加更详细的错误处理和日志记录

### 8.3 第三阶段（高级功能）

**前端任务**：

1. 多标签页协作优化
2. 性能监控和优化
3. 离线支持

**后端任务**：

1. 性能优化和监控
2. 更智能的摘要生成策略

---

## 9. 关键技术决策说明

### 9.1 为什么选择 IndexedDB

**优势**：

- 更大的存储容量（50MB-1GB+）
- 原生索引支持，查询性能更好
- 异步操作，不阻塞 UI
- 支持复杂数据结构

**与 localStorage 对比**：

- localStorage：同步操作，容量限制 5-10MB，无索引支持
- IndexedDB：异步操作，容量更大，有索引支持，更适合复杂数据管理

### 9.2 session_id 生成策略

**选择 nanoid 的原因**：

- 更短的 ID 长度（21 字符 vs UUID 的 36 字符）
- URL 友好（无特殊字符）
- 足够的唯一性保证
- 更好的性能

### 9.3 数据重复处理

**chatSummaryBuffer 与 frontend_session_chat_history 重复**：

- 这是设计上的权衡，为了保持后端无状态
- 重复的数据量相对较小，可接受
- 简化了系统架构，降低了复杂度

---

## 10. 测试和验证要点

### 10.1 功能测试

**前端测试**：

- IndexedDB 数据持久化和恢复
- visit_session 管理和垃圾清理
- 多标签页协作
- 会话列表和搜索功能

**后端测试**：

- chatSummaryBuffer 处理逻辑
- 两种 mode 的正确性
- 错误处理和边界情况

### 10.2 性能测试

**前端性能**：

- 大量会话的加载性能
- IndexedDB 查询性能
- 内存使用情况

**后端性能**：

- chatSummaryBuffer 处理性能
- 并发请求处理能力

---

## 11. 总结与下一步行动

### 11.1 关键改进总结

本 MVP 计划将存储方案从 localStorage 升级到 IndexedDB，并完善了前后端数据契约：

**核心改进**：

1. ✅ **存储升级**：从 localStorage 升级到 IndexedDB，支持更大容量和更好性能
2. ✅ **无索引设计**：利用 IndexedDB 原生索引，无需单独维护会话索引
3. ✅ **nanoid 生成**：使用 nanoid 生成更短、更友好的 session_id
4. ✅ **完整数据契约**：明确定义前后端交互的完整数据结构
5. ✅ **visit_session 管理**：智能的垃圾清理策略，避免频繁清理操作

**架构优势**：

- 后端完全无状态，易于扩展
- 前端数据管理更加高效和可靠
- 清晰的职责划分和数据流

### 11.2 立即开始的任务

**第一优先级（前端）**：

1. 实现 IndexedDB 数据库初始化
2. 实现 nanoid session_id 生成
3. 实现 visit_session 管理

**第一优先级（后端）**：

1. 修改 API 接口支持新数据契约
2. 实现 chatSummaryBuffer 处理逻辑

---

## 附录：技术参考

### A.1 代码文件清单

- 前端聊天页面：`app/static/js/pages.js` (loadRagChatPage 函数)
- 后端 RAG 服务：`app/services/rag_service.py`
- 数据模型定义：`app/schemas/rag.py`
- API 接口定义：`app/static/js/api.js` (RAGAPI)

---

## 12. 实现细节补充

### 12.1 前端 IndexedDB 操作封装（前端实现）

**会话管理器设计思路**：

- 封装所有 IndexedDB 操作到 `ChatSessionManager` 类
- 提供会话创建、读取、更新、删除的统一接口
- 内置容量监控和自动清理机制
- 支持数据格式版本管理和迁移

**关键方法接口**：

- `createSession(title?, mode?)` - 创建新会话
- `loadSession(sessionId)` - 加载指定会话
- `saveSession(sessionId, sessionData)` - 保存会话数据
- `listSessions(filters?)` - 获取会话列表
- `deleteSession(sessionId)` - 删除会话
- `cleanupOldSessions(maxAge)` - 清理过期会话

### 12.2 后端 chatSummaryBuffer 处理优化（后端实现）

**处理流程**：

1. 接收前端传来的 chatSummaryBuffer
2. 根据 mode 选择相应的聊天引擎
3. 使用 chatSummaryBuffer 初始化聊天记忆
4. 处理用户问题并生成回答
5. 获取更新后的 chatSummaryBuffer
6. 返回回答和新的 chatSummaryBuffer

**关键实现点**：

- 确保 chatSummaryBuffer 的序列化和反序列化正确
- 处理 chatSummaryBuffer 为 null 的情况
- 保证两种 mode 的一致性处理

### 12.3 visit_session 实现细节（前端实现）

**多标签页协调机制**：

```javascript
// 使用 BroadcastChannel 协调多标签页
const channel = new BroadcastChannel("chat_session_channel");

// 标签页计数管理
let tabCount = parseInt(localStorage.getItem("chat_tab_count") || "0");

// 页面加载时
window.addEventListener("load", () => {
  tabCount++;
  localStorage.setItem("chat_tab_count", tabCount.toString());

  if (tabCount === 1) {
    // 第一个标签页，执行垃圾清理
    performGarbageCleanup();
  }
});

// 页面卸载时
window.addEventListener("beforeunload", () => {
  tabCount--;
  localStorage.setItem("chat_tab_count", tabCount.toString());

  if (tabCount === 0) {
    // 最后一个标签页，清理 visit_session
    cleanupVisitSession();
  }
});
```

---

## 13. 数据迁移和版本管理

### 13.1 从 localStorage 到 IndexedDB 的迁移（前端实现）

**迁移策略**：

1. 检测是否存在旧的 localStorage 数据
2. 读取并转换数据格式
3. 写入到 IndexedDB
4. 清理旧的 localStorage 数据
5. 标记迁移完成

**数据格式转换**：

- 将旧的会话索引和内容合并为新的会话对象
- 添加新的必需字段（如 mode、timestamps）
- 处理缺失的字段，设置合理的默认值

### 13.2 版本管理（前端实现）

**数据库版本控制**：

- 使用 IndexedDB 的版本机制
- 在 `onupgradeneeded` 事件中处理结构变更
- 支持向前兼容的数据迁移

---

## 14. 性能优化建议

### 14.1 前端性能优化

**IndexedDB 操作优化**：

- 使用事务批量操作
- 合理使用索引避免全表扫描
- 实现数据分页加载
- 使用 Web Workers 处理大量数据

**内存管理**：

- 及时释放不需要的会话数据
- 使用虚拟滚动处理长聊天记录
- 实现会话数据的懒加载

### 14.2 后端性能优化

**chatSummaryBuffer 处理**：

- 优化序列化/反序列化性能
- 实现 chatSummaryBuffer 的压缩
- 缓存常用的聊天引擎实例

---

## 15. 安全考虑

### 15.1 数据安全

**客户端数据保护**：

- 敏感信息不存储在浏览器中
- 实现数据的基本混淆（非加密）
- 提供数据清理和重置功能

**传输安全**：

- 使用 HTTPS 传输
- 验证请求来源
- 实现基本的防重放攻击

### 15.2 隐私保护

**数据最小化**：

- 只存储必要的数据
- 定期清理过期数据
- 提供用户数据导出和删除功能

---

## 16. 监控和调试

### 16.1 前端监控

**关键指标**：

- IndexedDB 操作成功率
- 会话加载时间
- 存储空间使用情况
- 垃圾清理执行情况

**调试工具**：

- 浏览器开发者工具中的 IndexedDB 查看
- 自定义的会话管理调试面板
- 错误日志收集和上报

### 16.2 后端监控

**关键指标**：

- chatSummaryBuffer 处理时间
- 不同 mode 的响应时间
- 错误率和异常情况
- 内存使用情况

---

## 17. 总结

本方案成功将存储从 localStorage 升级到 IndexedDB，实现了：

1. **更强的存储能力**：支持更大容量和复杂数据结构
2. **更好的性能**：利用原生索引和异步操作
3. **更智能的管理**：visit_session 机制和自动垃圾清理
4. **更清晰的架构**：前后端职责分离，数据流清晰
5. **更好的扩展性**：为未来功能扩展奠定基础

这个方案为 RAG 聊天系统提供了一个可靠、高效、可扩展的数据管理基础。
